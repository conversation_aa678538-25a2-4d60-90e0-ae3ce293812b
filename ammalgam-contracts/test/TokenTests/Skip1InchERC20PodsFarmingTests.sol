// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20Hooks} from '@1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol';
import {ERC20Hooks} from '@1inch/token-plugins/contracts/ERC20Hooks.sol';
import {FarmingHook} from '@1inch/farming/contracts/FarmingHook.sol';
import {IFarmingHook} from '@1inch/farming/contracts/interfaces/IFarmingHook.sol';
import {FarmAccounting} from '@1inch/farming/contracts/accounting/FarmAccounting.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {DEPOSIT_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {getStubToken, MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';

contract ERC20PodsFarmingTests is Test {
    error AccessDenied();

    IAmmalgamPair private pair;
    IERC20 rewardToken;
    address private pairAddress;

    address private distributor;
    address private tester1;
    address private tester2;

    FactoryPairTestFixture private fixture;

    uint256 INITIAL_SUPPLY = 1e18;

    function setUp() public {
        distributor = vm.addr(1111);
        tester1 = vm.addr(1112);
        tester2 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        uint256 initialX = 1000e18;
        uint256 initialY = 1000e18;

        address random = vm.addr(1114);
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintFor(random, initialX, initialY);

        rewardToken = getStubToken('StubERC20', 'stub', MAX_TOKEN);
        rewardToken.transfer(distributor, MAX_TOKEN);
    }

    /**
     * @dev test is currently skipped due to via-ir requirement, to run it please enable via-ir and remove no-match conditions in
     * foundry.toml profile.
     */
    function testFarmingLiquidity() public {
        IERC20Hooks farmableToken = IERC20Hooks(address(fixture.pair().tokens(DEPOSIT_L)));

        // Distributor creates farm for liquidity providers
        vm.startPrank(distributor);
        FarmingHook farm = new FarmingHook(farmableToken, rewardToken, distributor);
        farm.setDistributor(distributor);

        // Start farming
        rewardToken.approve(address(farm), 1000);
        farm.startFarming(1000, 60 * 60 * 24);
        vm.stopPrank();

        // tester1 adds liquidity and adds pod to start farming
        fixture.transferTokensTo(tester1, 1000e18, 1000e18);
        fixture.mintFor(tester1, 1000e18, 1000e18);
        vm.prank(tester1);
        farmableToken.addPlugin(address(farm));

        // Move forward, accrue rewards rewards
        uint256 duration = block.timestamp + 1 days + 1;
        fixture.mineBlock(block.number + Math.ceilDiv(duration, 12), duration);

        validateFarmingState(farm, tester1, 1000);
    }

    function validateFarmingState(FarmingHook farm, address tester, uint256 reward) internal {
        assertEq(farm.farmed(tester), reward, 'tester1 has farmed reward that can be claimed.');
        assertEq(rewardToken.balanceOf(tester), 0, 'tester has balance of 0');

        vm.startPrank(tester);
        farm.claim();
        vm.stopPrank();

        assertEq(rewardToken.balanceOf(tester), reward, 'tester should have reward');
        assertEq(farm.farmed(tester), 0, 'tester claimed reward');
    }
}
